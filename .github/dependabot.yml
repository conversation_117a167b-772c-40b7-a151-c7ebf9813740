# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: npm
    directory: "/" # Location of package manifests
    rebase-strategy: "disabled" # use dependabot-rebase-stale
    open-pull-requests-limit: 10
    schedule:
      interval: "daily"
    versioning-strategy: "increase"
    commit-message:
      prefix: chore
      prefix-development: chore
      include: scope
    ignore:
      - dependency-name: "@types/node"
      - dependency-name: "@trpc/*"
    groups:
      prisma:
        patterns:
          - "prisma"
          - "@prisma/client"
      next:
        patterns:
          - "eslint-config-next"
          - "next"
      lodash:
        patterns:
          - "lodash"
          - "@types/lodash"
      express:
        patterns:
          - "express"
          - "@types/express"
      observability:
        patterns:
          - "dd-trace"
          - "@opentelemetry/*"
          - "@appsignal/opentelemetry-instrumentation-bullmq"
          - "@prisma/instrumentation"
          - "@sentry/*"
      radix-ui:
        patterns:
          - "@radix-ui/*"

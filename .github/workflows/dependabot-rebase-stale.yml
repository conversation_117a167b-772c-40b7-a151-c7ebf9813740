name: Rebase Dependabot stale PRs

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  rebase-dependabot:
    runs-on: ubuntu-latest
    environment: "protected branches"
    steps:
      - name: "Rebase open Dependabot PR"
        uses: orange-buffalo/dependabot-auto-rebase@v1
        with:
          api-token: ${{ secrets.GH_ACCESS_TOKEN }}
          repository: ${{ github.repository }}

{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.rulers": [100], "editor.tabSize": 2, "eslint.validate": ["javascript", "javascriptreact", "astro", "typescript", "typescriptreact"], "eslint.rules.customizations": [{"rule": "*", "severity": "warn"}], "typescript.tsdk": "node_modules/typescript/lib", "prettier.documentSelectors": ["**/*.{cjs,mjs,ts,tsx,astro,md,mdx,json,yaml,yml}"], "prettier.trailingComma": "all", "mdx.experimentalLanguageServer": true, "typescript.preferences.importModuleSpecifier": "non-relative", "docwriter.style": "JSDoc", "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "eslint.lintTask.enable": true, "eslint.workingDirectories": ["./web", "./worker"]}
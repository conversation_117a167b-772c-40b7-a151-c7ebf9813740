# yaml-language-server: $schema=https://raw.githubusercontent.com/fern-api/fern/main/fern.schema.json

service:
  auth: true
  base-path: /api/public
  endpoints:
    create:
      docs: Add a score to the database, upserts on id
      method: POST
      path: /scores
      request: CreateScoreRequest

types:
  CreateScoreRequest:
    properties:
      id: optional<string>
      traceId: string
      name: string
      value:
        type: CreateScoreValue
        docs: The value of the score. Must be passed as string for categorical scores, and numeric for boolean and numeric scores. Boolean score values must equal either 1 or 0 (true or false)
      observationId: optional<string>
      comment: optional<string>
      metadata: optional<unknown>
      dataType:
        type: optional<ScoreDataType>
        docs: When set, must match the score value's type. If not set, will be inferred from the score value or config
      configId:
        type: optional<string>
        docs: Reference a score config on a score. When set, the score name must equal the config name and scores must comply with the config's range and data type. For categorical scores, the value must map to a config category. Numeric scores might be constrained by the score config's max and min values
    examples:
      - value:
          name: "novelty"
          value: 0.9
          traceId: "cdef-1234-5678-90ab"
      - value:
          name: "consistency"
          value: 1.2
          dataType: "NUMERIC"
          traceId: "cdef-1234-5678-90ab"
      - value:
          name: "accuracy"
          value: 0.9
          dataType: "NUMERIC"
          configId: "9203-4567-89ab-cdef"
          traceId: "cdef-1234-5678-90ab"
      - value:
          name: "toxicity"
          value: "not toxic"
          traceId: "cdef-1234-5678-90ab"
      - value:
          name: "correctness"
          value: "partially correct"
          dataType: "CATEGORICAL"
          configId: "1234-5678-90ab-cdef"
          traceId: "cdef-1234-5678-90ab"
      - value:
          name: "hallucination"
          value: 0
          dataType: "BOOLEAN"
          traceId: "cdef-1234-5678-90ab"
      - value:
          name: "helpfulness"
          value: 1
          dataType: "BOOLEAN"
          configId: "1234-5678-90ab-cdef"
          traceId: "cdef-1234-5678-90ab"
  BaseScore:
    properties:
      id: string
      traceId: string
      name: string
      source: ScoreSource
      observationId: optional<string>
      timestamp: datetime
      createdAt: datetime
      updatedAt: datetime
      authorUserId: optional<string>
      comment: optional<string>
      metadata: optional<unknown>
      configId:
        type: optional<string>
        docs: Reference a score config on a score. When set, config and score name must be equal and value must comply to optionally defined numerical range
  NumericScore:
    extends: BaseScore
    properties:
      value:
        type: double
        docs: The numeric value of the score
  BooleanScore:
    extends: BaseScore
    properties:
      value:
        type: double
        docs: The numeric value of the score. Equals 1 for "True" and 0 for "False"
      stringValue:
        type: string
        docs: The string representation of the score value. Is inferred from the numeric value and equals "True" or "False"
  CategoricalScore:
    extends: BaseScore
    properties:
      value:
        type: optional<double>
        docs: Only defined if a config is linked. Represents the numeric category mapping of the stringValue
      stringValue:
        type: string
        docs: The string representation of the score value. If no config is linked, can be any string. Otherwise, must map to a config category
  Score:
    discriminant: "dataType"
    union:
      NUMERIC:
        type: NumericScore
        docs: "Score with NUMERIC data type"
      CATEGORICAL:
        type: CategoricalScore
        docs: "Score with CATEGORICAL data type"
      BOOLEAN:
        type: BooleanScore
        docs: "Score with BOOLEAN data type"
  ScoreSource:
    enum:
      - ANNOTATION
      - API
      - EVAL
  ScoreDataType:
    enum:
      - NUMERIC
      - CATEGORICAL
      - BOOLEAN

  CreateScoreValue:
    discriminated: false
    union:
      - double
      - string
    docs: The value of the score. Must be passed as string for categorical scores, and numeric for boolean and numeric scores

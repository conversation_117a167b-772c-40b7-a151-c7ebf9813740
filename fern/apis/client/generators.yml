default-group: local
groups:
  local:
    generators:
      # - name: fernapi/fern-typescript-browser-sdk
      #   version: 0.7.1
      #   output:
      #     location: npm
      #     url: npm.buildwithfern.com
      #     package-name: "@finto-fern/react-client"
      #   config:
      #     namespaceExport: Langfuse
      #     allowCustomFetcher: true
      - name: fernapi/fern-openapi
        version: 0.1.7
        output:
          location: local-file-system
          path: ../../../web/public/generated/api-client
        config:
          namespaceExport: Langfuse
          allowCustomFetcher: true

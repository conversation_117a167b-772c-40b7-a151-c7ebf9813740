# yaml-language-server: $schema=https://raw.githubusercontent.com/fern-api/fern/main/fern.schema.json
imports:
  pagination: ./utils/pagination.yml
  commons: ./commons.yml
service:
  auth: true
  base-path: /api/public
  endpoints:
    daily:
      docs: Get daily metrics of the Langfuse project
      method: GET
      path: /metrics/daily
      request:
        name: GetDailyMetricsRequest
        query-parameters:
          page:
            type: optional<integer>
            docs: page number, starts at 1
          limit:
            type: optional<integer>
            docs: limit of items per page
          traceName:
            type: optional<string>
            docs: Optional filter by the name of the trace
          userId:
            type: optional<string>
            docs: Optional filter by the userId associated with the trace
          tags:
            type: optional<string>
            allow-multiple: true
            docs: Optional filter for metrics where traces include all of these tags
          environment:
            type: optional<string>
            allow-multiple: true
            docs: Optional filter for metrics where events include any of these environments
          fromTimestamp:
            type: optional<datetime>
            docs: Optional filter to only include traces and observations on or after a certain datetime (ISO 8601)
          toTimestamp:
            type: optional<datetime>
            docs: Optional filter to only include traces and observations before a certain datetime (ISO 8601)
      response: DailyMetrics
types:
  DailyMetrics:
    properties:
      data:
        type: list<DailyMetricsDetails>
        docs: A list of daily metrics, only days with ingested data are included.
      meta: pagination.MetaResponse
  DailyMetricsDetails:
    properties:
      date: date
      countTraces: integer
      countObservations: integer
      totalCost:
        type: double
        docs: Total model cost in USD
      usage: list<UsageByModel>
  UsageByModel:
    docs: Daily usage of a given model. Usage corresponds to the unit set for the specific model (e.g. tokens).
    properties:
      model: optional<string>
      inputUsage:
        type: integer
        docs: Total number of generation input units (e.g. tokens)
      outputUsage:
        type: integer
        docs: Total number of generation output units (e.g. tokens)
      totalUsage:
        type: integer
        docs: Total number of generation total units (e.g. tokens)
      countTraces: integer
      countObservations: integer
      totalCost:
        type: double
        docs: Total model cost in USD

# yaml-language-server: $schema=https://raw.githubusercontent.com/fern-api/fern/main/fern.schema.json
imports:
  pagination: ./utils/pagination.yml
  commons: ./commons.yml
service:
  auth: true
  base-path: /api/public
  endpoints:
    list:
      docs: Get sessions
      method: GET
      path: /sessions
      request:
        name: GetSessionsRequest
        query-parameters:
          page:
            type: optional<integer>
            docs: Page number, starts at 1
          limit:
            type: optional<integer>
            docs: Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit.
          fromTimestamp:
            type: optional<datetime>
            docs: Optional filter to only include sessions created on or after a certain datetime (ISO 8601)
          toTimestamp:
            type: optional<datetime>
            docs: Optional filter to only include sessions created before a certain datetime (ISO 8601)
          environment:
            type: optional<string>
            allow-multiple: true
            docs: Optional filter for sessions where the environment is one of the provided values.
      response: PaginatedSessions
    get:
      docs: Get a session. Please note that `traces` on this endpoint are not paginated, if you plan to fetch large sessions, consider `GET /api/public/traces?sessionId=<sessionId>`
      method: GET
      path: /sessions/{sessionId}
      path-parameters:
        sessionId:
          type: string
          docs: The unique id of a session
      response: commons.SessionWithTraces
types:
  PaginatedSessions:
    properties:
      data: list<commons.Session>
      meta: pagination.MetaResponse

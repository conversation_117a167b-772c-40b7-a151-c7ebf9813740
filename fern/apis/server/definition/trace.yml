# yaml-language-server: $schema=https://raw.githubusercontent.com/fern-api/fern/main/fern.schema.json
imports:
  pagination: ./utils/pagination.yml
  commons: ./commons.yml
service:
  auth: true
  base-path: /api/public
  endpoints:
    get:
      docs: Get a specific trace
      method: GET
      path: /traces/{traceId}
      path-parameters:
        traceId:
          type: string
          docs: The unique langfuse identifier of a trace
      response: commons.TraceWithFullDetails
    delete:
      docs: Delete a specific trace
      method: DELETE
      path: /traces/{traceId}
      path-parameters:
        traceId:
          type: string
          docs: The unique langfuse identifier of the trace to delete
      response: DeleteTraceResponse
    list:
      docs: Get list of traces
      method: GET
      path: /traces
      request:
        name: GetTracesRequest
        query-parameters:
          page:
            type: optional<integer>
            docs: Page number, starts at 1
          limit:
            type: optional<integer>
            docs: Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit.
          userId: optional<string>
          name: optional<string>
          sessionId: optional<string>
          fromTimestamp:
            type: optional<datetime>
            docs: Optional filter to only include traces with a trace.timestamp on or after a certain datetime (ISO 8601)
          toTimestamp:
            type: optional<datetime>
            docs: Optional filter to only include traces with a trace.timestamp before a certain datetime (ISO 8601)
          orderBy:
            type: optional<string>
            docs: "Format of the string [field].[asc/desc]. Fields: id, timestamp, name, userId, release, version, public, bookmarked, sessionId. Example: timestamp.asc"
          tags:
            type: optional<string>
            allow-multiple: true
            docs: Only traces that include all of these tags will be returned.
          version:
            type: optional<string>
            docs: Optional filter to only include traces with a certain version.
          release:
            type: optional<string>
            docs: Optional filter to only include traces with a certain release.
          environment:
            type: optional<string>
            allow-multiple: true
            docs: Optional filter for traces where the environment is one of the provided values.
      response: Traces
    deleteMultiple:
      docs: Delete multiple traces
      method: DELETE
      path: /traces
      request:
        name: DeleteTracesRequest
        body:
          properties:
            traceIds:
              type: list<string>
              docs: List of trace IDs to delete
      response: DeleteTraceResponse

types:
  Traces:
    properties:
      data: list<commons.TraceWithDetails>
      meta: pagination.MetaResponse
  DeleteTraceResponse:
    properties:
      message: string
  Sort:
    properties:
      id: string

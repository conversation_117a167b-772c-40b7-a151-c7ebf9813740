{"name": "@repo/eslint-config", "version": "0.0.0", "license": "MIT", "private": true, "files": ["library.js", "next.js"], "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.12.0", "@vercel/style-guide": "^6.0.0", "eslint-config-next": "^14.2.15", "eslint-config-prettier": "^9.1.0", "eslint-config-turbo": "^1.13.4", "eslint-plugin-only-warn": "^1.1.0", "typescript": "^5.4.5"}}
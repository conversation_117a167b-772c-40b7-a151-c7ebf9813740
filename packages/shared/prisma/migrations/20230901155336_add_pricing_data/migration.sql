
-- This migration adds default prices for the models that are currently available on the LANGCHAIN platform.
-- https://github.com/langchain-ai/langchain/blob/e60e1cdf23ad73b2e0a40034c0ddfc3c8b0c9c4d/libs/langchain/langchain/callbacks/openai_info.py#L7

INSERT INTO pricings (
  id,
  model_name, 
  pricing_unit, 
  price,
  currency,
  token_type
)
VALUES
  ('clm0obv1u00003b6lc9etkzfg','gpt-4', 'PER_1000_TOKENS', 0.03, 'USD', 'PROMPT'),
  ('clm0obv1u00013b6l4jdl83vs','gpt-4-0314', 'PER_1000_TOKENS', 0.03, 'USD', 'PROMPT'),
  ('clm0obv1u00023b6lsi1d24dh','gpt-4-0613', 'PER_1000_TOKENS', 0.03, 'USD', 'PROMPT'),
  ('clm0obv1u00033b6lvv4h0iex','gpt-4-32k', 'PER_1000_TOKENS', 0.06, 'USD', 'PROMPT'),
  ('clm0obv1u00043b6ln5pleunh','gpt-4-32k-0314', 'PER_1000_TOKENS', 0.06, 'USD', 'PROMPT'),
  ('clm0obv1u00053b6l0g4zo5oe','gpt-4-32k-0613', 'PER_1000_TOKENS', 0.06, 'USD', 'PROMPT'),
  ('clm0obv1u00063b6lv7y80efe','gpt-4', 'PER_1000_TOKENS', 0.06, 'USD', 'COMPLETION'),
  ('clm0obv1u00073b6l302roky7','gpt-4-0314', 'PER_1000_TOKENS', 0.06, 'USD', 'COMPLETION'),
  ('clm0obv1u00083b6laz822qt4','gpt-4-0613', 'PER_1000_TOKENS', 0.06, 'USD', 'COMPLETION'),
  ('clm0obv1u00093b6l9ivm2fbs','gpt-4-32k', 'PER_1000_TOKENS', 0.12, 'USD', 'COMPLETION'),
  ('clm0obv1u000a3b6l7ou8mq4o','gpt-4-32k-0314', 'PER_1000_TOKENS', 0.12, 'USD', 'COMPLETION'),
  ('clm0obv1u000b3b6lym69vmpx','gpt-4-32k-0613', 'PER_1000_TOKENS', 0.12, 'USD', 'COMPLETION'),
  ('clm0obv1u000c3b6lfeg8e6gh','gpt-3.5-turbo', 'PER_1000_TOKENS', 0.0015, 'USD', 'PROMPT'),
  ('clm0obv1u000d3b6lb8ww3uj2','gpt-3.5-turbo-0301', 'PER_1000_TOKENS', 0.0015, 'USD', 'PROMPT'),
  ('clm0obv1u000e3b6lznniclze','gpt-3.5-turbo-0613', 'PER_1000_TOKENS', 0.0015, 'USD', 'PROMPT'),
  ('clm0obv1u000f3b6lr2bxjnxr','gpt-3.5-turbo-16k', 'PER_1000_TOKENS', 0.003, 'USD', 'PROMPT'),
  ('clm0obv1u000g3b6lo3rqrgpj','gpt-3.5-turbo-16k-0613', 'PER_1000_TOKENS', 0.003, 'USD', 'PROMPT'),
  ('clm0obv1u000h3b6lt1l33gpm','gpt-3.5-turbo', 'PER_1000_TOKENS', 0.002, 'USD', 'COMPLETION'),
  ('clm0obv1u000i3b6lgfs7pi5b','gpt-3.5-turbo-0301', 'PER_1000_TOKENS', 0.002, 'USD', 'COMPLETION'),
  ('clm0obv1u000j3b6lg1jokl07','gpt-3.5-turbo-0613', 'PER_1000_TOKENS', 0.002, 'USD', 'COMPLETION'),
  ('clm0obv1u000k3b6lgfntsm74','gpt-3.5-turbo-16k', 'PER_1000_TOKENS', 0.004, 'USD', 'COMPLETION'),
  ('clm0obv1u000l3b6l8wjhn0ie','gpt-3.5-turbo-16k-0613', 'PER_1000_TOKENS', 0.004, 'USD', 'COMPLETION'),
  ('clm0obv1u000m3b6lgrcel0ce','gpt-35-turbo', 'PER_1000_TOKENS', 0.0015, 'USD', 'PROMPT'),
  ('clm0obv1u000n3b6lw3bc6q70','gpt-35-turbo-0301', 'PER_1000_TOKENS', 0.0015, 'USD', 'PROMPT'),
  ('clm0obv1u000o3b6luimelud7','gpt-35-turbo-0613', 'PER_1000_TOKENS', 0.0015, 'USD', 'PROMPT'),
  ('clm0obv1u000p3b6lft9rfj1h','gpt-35-turbo-16k', 'PER_1000_TOKENS', 0.003, 'USD', 'PROMPT'),
  ('clm0obv1u000q3b6l1zh6x8lu','gpt-35-turbo-16k-0613', 'PER_1000_TOKENS', 0.003, 'USD', 'PROMPT'),
  ('clm0obv1v000r3b6llobngoj3','gpt-35-turbo', 'PER_1000_TOKENS', 0.002, 'USD', 'COMPLETION'),
  ('clm0obv1v000s3b6lgcjqu15e','gpt-35-turbo-0301', 'PER_1000_TOKENS', 0.002, 'USD', 'COMPLETION'),
  ('clm0obv1v000t3b6l6ijo29gd','gpt-35-turbo-0613', 'PER_1000_TOKENS', 0.002, 'USD', 'COMPLETION'), 
  ('clm0obv1v000u3b6lo16c29a1','gpt-35-turbo-16k', 'PER_1000_TOKENS', 0.004, 'USD', 'COMPLETION'),
  ('clm0obv1v000v3b6leqypomnv','gpt-35-turbo-16k-0613', 'PER_1000_TOKENS', 0.004, 'USD', 'COMPLETION'),
  ('clm0obv1v000w3b6l6y0ojr1j','text-ada-001', 'PER_1000_TOKENS', 0.0004, 'USD', 'TOTAL'),
  ('clm0obv1v000x3b6lwdjl5os6','ada', 'PER_1000_TOKENS', 0.0004, 'USD', 'TOTAL'),
  ('clm0obv1v000y3b6lsap2cbkj','text-babbage-001', 'PER_1000_TOKENS', 0.0005, 'USD', 'TOTAL'),
  ('clm0obv1v000z3b6lto21j3xd','babbage', 'PER_1000_TOKENS', 0.0005, 'USD', 'TOTAL'),
  ('clm0obv1v00103b6lges5uumn','text-curie-001', 'PER_1000_TOKENS', 0.002, 'USD', 'TOTAL'),
  ('clm0obv1v00113b6l2vizhjhc','curie', 'PER_1000_TOKENS', 0.002, 'USD', 'TOTAL'),
  ('clm0obv1v00123b6lqb7mfrzr','text-davinci-003', 'PER_1000_TOKENS', 0.02, 'USD', 'TOTAL'),
  ('clm0obv1v00133b6lnqa8ecal','text-davinci-002', 'PER_1000_TOKENS', 0.02, 'USD', 'TOTAL'),
  ('clm0obv1v00143b6l6z5s44sf','code-davinci-002', 'PER_1000_TOKENS', 0.02, 'USD', 'TOTAL'),
  ('clm0obv1v00153b6lyntq7lx0','ada-finetuned', 'PER_1000_TOKENS', 0.0016, 'USD', 'TOTAL'),
  ('clm0obv1v00163b6l5fsheo7p','babbage-finetuned', 'PER_1000_TOKENS', 0.0024, 'USD', 'TOTAL'),
  ('clm0obv1v00173b6lly0887fz','curie-finetuned', 'PER_1000_TOKENS', 0.012, 'USD', 'TOTAL'),
  ('clm0obv1v00183b6lg20tw4g4','davinci-finetuned', 'PER_1000_TOKENS', 0.12, 'USD', 'TOTAL')
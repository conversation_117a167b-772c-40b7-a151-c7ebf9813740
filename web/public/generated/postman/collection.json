{"info": {"name": "<PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "## Authentication\n\nAuthenticate with the API using [Basic Auth](https://en.wikipedia.org/wiki/Basic_access_authentication), get API keys in the project settings:\n\n- username: Langfuse Public Key\n- password: Langfuse Secret Key\n\n## Exports\n\n- OpenAPI spec: https://cloud.langfuse.com/generated/api/openapi.yml\n- Postman collection: https://cloud.langfuse.com/generated/postman/collection.json"}, "variable": [{"key": "baseUrl", "value": "", "type": "string"}, {"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}], "auth": {"type": "basic", "basic": [{"key": "username", "value": "{{username}}", "type": "string"}, {"key": "password", "value": "{{password}}", "type": "string"}]}, "item": [{"_type": "container", "description": null, "name": "Annotation Queues", "item": [{"_type": "endpoint", "name": "List Queues", "request": {"description": "Get all annotation queues", "url": {"raw": "{{baseUrl}}/api/public/annotation-queues?page=&limit=", "host": ["{{baseUrl}}"], "path": ["api", "public", "annotation-queues"], "query": [{"key": "page", "value": "", "description": "page number, starts at 1"}, {"key": "limit", "value": "", "description": "limit of items per page"}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get Queue", "request": {"description": "Get an annotation queue by ID", "url": {"raw": "{{baseUrl}}/api/public/annotation-queues/:queueId", "host": ["{{baseUrl}}"], "path": ["api", "public", "annotation-queues", ":queueId"], "query": [], "variable": [{"key": "queueId", "value": "", "description": "The unique identifier of the annotation queue"}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "List Queue Items", "request": {"description": "Get items for a specific annotation queue", "url": {"raw": "{{baseUrl}}/api/public/annotation-queues/:queueId/items?status=&page=&limit=", "host": ["{{baseUrl}}"], "path": ["api", "public", "annotation-queues", ":queueId", "items"], "query": [{"key": "status", "value": "", "description": "Filter by status"}, {"key": "page", "value": "", "description": "page number, starts at 1"}, {"key": "limit", "value": "", "description": "limit of items per page"}], "variable": [{"key": "queueId", "value": "", "description": "The unique identifier of the annotation queue"}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get Queue Item", "request": {"description": "Get a specific item from an annotation queue", "url": {"raw": "{{baseUrl}}/api/public/annotation-queues/:queueId/items/:itemId", "host": ["{{baseUrl}}"], "path": ["api", "public", "annotation-queues", ":queueId", "items", ":itemId"], "query": [], "variable": [{"key": "queueId", "value": "", "description": "The unique identifier of the annotation queue"}, {"key": "itemId", "value": "", "description": "The unique identifier of the annotation queue item"}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Create <PERSON><PERSON>em", "request": {"description": "Add an item to an annotation queue", "url": {"raw": "{{baseUrl}}/api/public/annotation-queues/:queueId/items", "host": ["{{baseUrl}}"], "path": ["api", "public", "annotation-queues", ":queueId", "items"], "query": [], "variable": [{"key": "queueId", "value": "", "description": "The unique identifier of the annotation queue"}]}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"objectId\": \"example\",\n    \"objectType\": \"TRACE\",\n    \"status\": \"PENDING\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Update Queue Item", "request": {"description": "Update an annotation queue item", "url": {"raw": "{{baseUrl}}/api/public/annotation-queues/:queueId/items/:itemId", "host": ["{{baseUrl}}"], "path": ["api", "public", "annotation-queues", ":queueId", "items", ":itemId"], "query": [], "variable": [{"key": "queueId", "value": "", "description": "The unique identifier of the annotation queue"}, {"key": "itemId", "value": "", "description": "The unique identifier of the annotation queue item"}]}, "header": [], "method": "PATCH", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"status\": \"PENDING\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Delete Queue Item", "request": {"description": "Remove an item from an annotation queue", "url": {"raw": "{{baseUrl}}/api/public/annotation-queues/:queueId/items/:itemId", "host": ["{{baseUrl}}"], "path": ["api", "public", "annotation-queues", ":queueId", "items", ":itemId"], "query": [], "variable": [{"key": "queueId", "value": "", "description": "The unique identifier of the annotation queue"}, {"key": "itemId", "value": "", "description": "The unique identifier of the annotation queue item"}]}, "header": [], "method": "DELETE", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Comments", "item": [{"_type": "endpoint", "name": "Create", "request": {"description": "Create a comment. Comments may be attached to different object types (trace, observation, session, prompt).", "url": {"raw": "{{baseUrl}}/api/public/comments", "host": ["{{baseUrl}}"], "path": ["api", "public", "comments"], "query": [], "variable": []}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"projectId\": \"example\",\n    \"objectType\": \"example\",\n    \"objectId\": \"example\",\n    \"content\": \"example\",\n    \"authorUserId\": \"example\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Get", "request": {"description": "Get all comments", "url": {"raw": "{{baseUrl}}/api/public/comments?page=&limit=&objectType=&objectId=&authorUserId=", "host": ["{{baseUrl}}"], "path": ["api", "public", "comments"], "query": [{"key": "page", "value": "", "description": "Page number, starts at 1."}, {"key": "limit", "value": "", "description": "Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit"}, {"key": "objectType", "value": "", "description": "Filter comments by object type (trace, observation, session, prompt)."}, {"key": "objectId", "value": "", "description": "Filter comments by object id. If objectType is not provided, an error will be thrown."}, {"key": "authorUserId", "value": "", "description": "Filter comments by author user id."}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get By Id", "request": {"description": "Get a comment by id", "url": {"raw": "{{baseUrl}}/api/public/comments/:commentId", "host": ["{{baseUrl}}"], "path": ["api", "public", "comments", ":commentId"], "query": [], "variable": [{"key": "commentId", "value": "", "description": "The unique langfuse identifier of a comment"}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Dataset Items", "item": [{"_type": "endpoint", "name": "Create", "request": {"description": "Create a dataset item", "url": {"raw": "{{baseUrl}}/api/public/dataset-items", "host": ["{{baseUrl}}"], "path": ["api", "public", "dataset-items"], "query": [], "variable": []}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"datasetName\": \"example\",\n    \"input\": \"UNKNOWN\",\n    \"expectedOutput\": \"UNKNOWN\",\n    \"metadata\": \"UNKNOWN\",\n    \"sourceTraceId\": \"example\",\n    \"sourceObservationId\": \"example\",\n    \"id\": \"example\",\n    \"status\": \"ACTIVE\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Get", "request": {"description": "Get a dataset item", "url": {"raw": "{{baseUrl}}/api/public/dataset-items/:id", "host": ["{{baseUrl}}"], "path": ["api", "public", "dataset-items", ":id"], "query": [], "variable": [{"key": "id", "value": "", "description": null}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "List", "request": {"description": "Get dataset items", "url": {"raw": "{{baseUrl}}/api/public/dataset-items?datasetName=&sourceTraceId=&sourceObservationId=&page=&limit=", "host": ["{{baseUrl}}"], "path": ["api", "public", "dataset-items"], "query": [{"key": "datasetName", "value": "", "description": null}, {"key": "sourceTraceId", "value": "", "description": null}, {"key": "sourceObservationId", "value": "", "description": null}, {"key": "page", "value": "", "description": "page number, starts at 1"}, {"key": "limit", "value": "", "description": "limit of items per page"}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Delete", "request": {"description": "Delete a dataset item and all its run items. This action is irreversible.", "url": {"raw": "{{baseUrl}}/api/public/dataset-items/:id", "host": ["{{baseUrl}}"], "path": ["api", "public", "dataset-items", ":id"], "query": [], "variable": [{"key": "id", "value": "", "description": null}]}, "header": [], "method": "DELETE", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Dataset Run Items", "item": [{"_type": "endpoint", "name": "Create", "request": {"description": "Create a dataset run item", "url": {"raw": "{{baseUrl}}/api/public/dataset-run-items", "host": ["{{baseUrl}}"], "path": ["api", "public", "dataset-run-items"], "query": [], "variable": []}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"runName\": \"example\",\n    \"runDescription\": \"example\",\n    \"metadata\": \"UNKNOWN\",\n    \"datasetItemId\": \"example\",\n    \"observationId\": \"example\",\n    \"traceId\": \"example\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}]}, {"_type": "container", "description": null, "name": "Datasets", "item": [{"_type": "endpoint", "name": "List", "request": {"description": "Get all datasets", "url": {"raw": "{{baseUrl}}/api/public/v2/datasets?page=&limit=", "host": ["{{baseUrl}}"], "path": ["api", "public", "v2", "datasets"], "query": [{"key": "page", "value": "", "description": "page number, starts at 1"}, {"key": "limit", "value": "", "description": "limit of items per page"}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get", "request": {"description": "Get a dataset", "url": {"raw": "{{baseUrl}}/api/public/v2/datasets/:datasetName", "host": ["{{baseUrl}}"], "path": ["api", "public", "v2", "datasets", ":datasetName"], "query": [], "variable": [{"key": "datasetName", "value": "", "description": null}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Create", "request": {"description": "Create a dataset", "url": {"raw": "{{baseUrl}}/api/public/v2/datasets", "host": ["{{baseUrl}}"], "path": ["api", "public", "v2", "datasets"], "query": [], "variable": []}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"name\": \"example\",\n    \"description\": \"example\",\n    \"metadata\": \"UNKNOWN\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Get Run", "request": {"description": "Get a dataset run and its items", "url": {"raw": "{{baseUrl}}/api/public/datasets/:datasetName/runs/:runName", "host": ["{{baseUrl}}"], "path": ["api", "public", "datasets", ":datasetName", "runs", ":runName"], "query": [], "variable": [{"key": "datasetName", "value": "", "description": null}, {"key": "runName", "value": "", "description": null}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Delete Run", "request": {"description": "Delete a dataset run and all its run items. This action is irreversible.", "url": {"raw": "{{baseUrl}}/api/public/datasets/:datasetName/runs/:runName", "host": ["{{baseUrl}}"], "path": ["api", "public", "datasets", ":datasetName", "runs", ":runName"], "query": [], "variable": [{"key": "datasetName", "value": "", "description": null}, {"key": "runName", "value": "", "description": null}]}, "header": [], "method": "DELETE", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get Runs", "request": {"description": "Get dataset runs", "url": {"raw": "{{baseUrl}}/api/public/datasets/:datasetName/runs?page=&limit=", "host": ["{{baseUrl}}"], "path": ["api", "public", "datasets", ":datasetName", "runs"], "query": [{"key": "page", "value": "", "description": "page number, starts at 1"}, {"key": "limit", "value": "", "description": "limit of items per page"}], "variable": [{"key": "datasetName", "value": "", "description": null}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Health", "item": [{"_type": "endpoint", "name": "Health", "request": {"description": "Check health of API and database", "url": {"raw": "{{baseUrl}}/api/public/health", "host": ["{{baseUrl}}"], "path": ["api", "public", "health"], "query": [], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Ingestion", "item": [{"_type": "endpoint", "name": "<PERSON><PERSON>", "request": {"description": "Batched ingestion for Langfuse Tracing.\nIf you want to use tracing via the API, such as to build your own Langfuse client implementation, this is the only API route you need to implement.\n\nWithin each batch, there can be multiple events.\nEach event has a type, an id, a timestamp, metadata and a body.\nInternally, we refer to this as the \"event envelope\" as it tells us something about the event but not the trace.\nWe use the event id within this envelope to deduplicate messages to avoid processing the same event twice, i.e. the event id should be unique per request.\nThe event.body.id is the ID of the actual trace and will be used for updates and will be visible within the Langfuse App.\nI.e. if you want to update a trace, you'd use the same body id, but separate event IDs.\n\nNotes:\n- Introduction to data model: https://langfuse.com/docs/tracing-data-model\n- Batch sizes are limited to 3.5 MB in total. You need to adjust the number of events per batch accordingly.\n- The API does not return a 4xx status code for input errors. Instead, it responds with a 207 status code, which includes a list of the encountered errors.", "url": {"raw": "{{baseUrl}}/api/public/ingestion", "host": ["{{baseUrl}}"], "path": ["api", "public", "ingestion"], "query": [], "variable": []}, "header": [{"type": "text", "key": "Content-Type", "value": "application/json"}], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"batch\": [\n        {\n            \"id\": \"abcdef-1234-5678-90ab\",\n            \"timestamp\": \"2022-01-01T00:00:00.000Z\",\n            \"type\": \"trace-create\",\n            \"body\": {\n                \"id\": \"abcdef-1234-5678-90ab\",\n                \"timestamp\": \"2022-01-01T00:00:00.000Z\",\n                \"environment\": \"production\",\n                \"name\": \"My Trace\",\n                \"userId\": \"1234-5678-90ab-cdef\",\n                \"input\": \"My input\",\n                \"output\": \"My output\",\n                \"sessionId\": \"1234-5678-90ab-cdef\",\n                \"release\": \"1.0.0\",\n                \"version\": \"1.0.0\",\n                \"metadata\": \"My metadata\",\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"public\": true\n            }\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success", "status": "OK", "code": 200, "originalRequest": {"description": "Batched ingestion for Langfuse Tracing.\nIf you want to use tracing via the API, such as to build your own Langfuse client implementation, this is the only API route you need to implement.\n\nWithin each batch, there can be multiple events.\nEach event has a type, an id, a timestamp, metadata and a body.\nInternally, we refer to this as the \"event envelope\" as it tells us something about the event but not the trace.\nWe use the event id within this envelope to deduplicate messages to avoid processing the same event twice, i.e. the event id should be unique per request.\nThe event.body.id is the ID of the actual trace and will be used for updates and will be visible within the Langfuse App.\nI.e. if you want to update a trace, you'd use the same body id, but separate event IDs.\n\nNotes:\n- Introduction to data model: https://langfuse.com/docs/tracing-data-model\n- Batch sizes are limited to 3.5 MB in total. You need to adjust the number of events per batch accordingly.\n- The API does not return a 4xx status code for input errors. Instead, it responds with a 207 status code, which includes a list of the encountered errors.", "url": {"raw": "{{baseUrl}}/api/public/ingestion", "host": ["{{baseUrl}}"], "path": ["api", "public", "ingestion"], "query": [], "variable": []}, "header": [{"type": "text", "key": "Content-Type", "value": "application/json"}], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"batch\": [\n        {\n            \"id\": \"abcdef-1234-5678-90ab\",\n            \"timestamp\": \"2022-01-01T00:00:00.000Z\",\n            \"type\": \"trace-create\",\n            \"body\": {\n                \"id\": \"abcdef-1234-5678-90ab\",\n                \"timestamp\": \"2022-01-01T00:00:00.000Z\",\n                \"environment\": \"production\",\n                \"name\": \"My Trace\",\n                \"userId\": \"1234-5678-90ab-cdef\",\n                \"input\": \"My input\",\n                \"output\": \"My output\",\n                \"sessionId\": \"1234-5678-90ab-cdef\",\n                \"release\": \"1.0.0\",\n                \"version\": \"1.0.0\",\n                \"metadata\": \"My metadata\",\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"public\": true\n            }\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}}, "description": null, "body": "{\n    \"successes\": [\n        {\n            \"id\": \"abcdef-1234-5678-90ab\",\n            \"status\": 201\n        }\n    ],\n    \"errors\": []\n}", "_postman_previewlanguage": "json"}, {"name": "Success", "status": "OK", "code": 200, "originalRequest": {"description": "Batched ingestion for Langfuse Tracing.\nIf you want to use tracing via the API, such as to build your own Langfuse client implementation, this is the only API route you need to implement.\n\nWithin each batch, there can be multiple events.\nEach event has a type, an id, a timestamp, metadata and a body.\nInternally, we refer to this as the \"event envelope\" as it tells us something about the event but not the trace.\nWe use the event id within this envelope to deduplicate messages to avoid processing the same event twice, i.e. the event id should be unique per request.\nThe event.body.id is the ID of the actual trace and will be used for updates and will be visible within the Langfuse App.\nI.e. if you want to update a trace, you'd use the same body id, but separate event IDs.\n\nNotes:\n- Introduction to data model: https://langfuse.com/docs/tracing-data-model\n- Batch sizes are limited to 3.5 MB in total. You need to adjust the number of events per batch accordingly.\n- The API does not return a 4xx status code for input errors. Instead, it responds with a 207 status code, which includes a list of the encountered errors.", "url": {"raw": "{{baseUrl}}/api/public/ingestion", "host": ["{{baseUrl}}"], "path": ["api", "public", "ingestion"], "query": [], "variable": []}, "header": [{"type": "text", "key": "Content-Type", "value": "application/json"}], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"batch\": [\n        {\n            \"id\": \"abcdef-1234-5678-90ab\",\n            \"timestamp\": \"2022-01-01T00:00:00.000Z\",\n            \"type\": \"span-create\",\n            \"body\": {\n                \"id\": \"abcdef-1234-5678-90ab\",\n                \"traceId\": \"1234-5678-90ab-cdef\",\n                \"startTime\": \"2022-01-01T00:00:00.000Z\",\n                \"environment\": \"test\"\n            }\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}}, "description": null, "body": "{\n    \"successes\": [\n        {\n            \"id\": \"abcdef-1234-5678-90ab\",\n            \"status\": 201\n        }\n    ],\n    \"errors\": []\n}", "_postman_previewlanguage": "json"}, {"name": "Success", "status": "OK", "code": 200, "originalRequest": {"description": "Batched ingestion for Langfuse Tracing.\nIf you want to use tracing via the API, such as to build your own Langfuse client implementation, this is the only API route you need to implement.\n\nWithin each batch, there can be multiple events.\nEach event has a type, an id, a timestamp, metadata and a body.\nInternally, we refer to this as the \"event envelope\" as it tells us something about the event but not the trace.\nWe use the event id within this envelope to deduplicate messages to avoid processing the same event twice, i.e. the event id should be unique per request.\nThe event.body.id is the ID of the actual trace and will be used for updates and will be visible within the Langfuse App.\nI.e. if you want to update a trace, you'd use the same body id, but separate event IDs.\n\nNotes:\n- Introduction to data model: https://langfuse.com/docs/tracing-data-model\n- Batch sizes are limited to 3.5 MB in total. You need to adjust the number of events per batch accordingly.\n- The API does not return a 4xx status code for input errors. Instead, it responds with a 207 status code, which includes a list of the encountered errors.", "url": {"raw": "{{baseUrl}}/api/public/ingestion", "host": ["{{baseUrl}}"], "path": ["api", "public", "ingestion"], "query": [], "variable": []}, "header": [{"type": "text", "key": "Content-Type", "value": "application/json"}], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"batch\": [\n        {\n            \"id\": \"abcdef-1234-5678-90ab\",\n            \"timestamp\": \"2022-01-01T00:00:00.000Z\",\n            \"type\": \"score-create\",\n            \"body\": {\n                \"id\": \"abcdef-1234-5678-90ab\",\n                \"traceId\": \"1234-5678-90ab-cdef\",\n                \"name\": \"My Score\",\n                \"value\": 0.9,\n                \"environment\": \"default\"\n            }\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}}, "description": null, "body": "{\n    \"successes\": [\n        {\n            \"id\": \"abcdef-1234-5678-90ab\",\n            \"status\": 201\n        }\n    ],\n    \"errors\": []\n}", "_postman_previewlanguage": "json"}]}]}, {"_type": "container", "description": null, "name": "Media", "item": [{"_type": "endpoint", "name": "Get", "request": {"description": "Get a media record", "url": {"raw": "{{baseUrl}}/api/public/media/:mediaId", "host": ["{{baseUrl}}"], "path": ["api", "public", "media", ":mediaId"], "query": [], "variable": [{"key": "mediaId", "value": "", "description": "The unique langfuse identifier of a media record"}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Patch", "request": {"description": "Patch a media record", "url": {"raw": "{{baseUrl}}/api/public/media/:mediaId", "host": ["{{baseUrl}}"], "path": ["api", "public", "media", ":mediaId"], "query": [], "variable": [{"key": "mediaId", "value": "", "description": "The unique langfuse identifier of a media record"}]}, "header": [], "method": "PATCH", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"uploadedAt\": \"1994-11-05T13:15:30Z\",\n    \"uploadHttpStatus\": 0,\n    \"uploadHttpError\": \"example\",\n    \"uploadTimeMs\": 0\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Get Upload Url", "request": {"description": "Get a presigned upload URL for a media record", "url": {"raw": "{{baseUrl}}/api/public/media", "host": ["{{baseUrl}}"], "path": ["api", "public", "media"], "query": [], "variable": []}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"traceId\": \"example\",\n    \"observationId\": \"example\",\n    \"contentType\": \"image/png\",\n    \"contentLength\": 0,\n    \"sha256Hash\": \"example\",\n    \"field\": \"example\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}]}, {"_type": "container", "description": null, "name": "Memberships", "item": [{"_type": "endpoint", "name": "Get Organization Memberships", "request": {"description": "Get all memberships for the organization associated with the API key (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/organizations/memberships", "host": ["{{baseUrl}}"], "path": ["api", "public", "organizations", "memberships"], "query": [], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Update Organization Membership", "request": {"description": "Create or update a membership for the organization associated with the API key (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/organizations/memberships", "host": ["{{baseUrl}}"], "path": ["api", "public", "organizations", "memberships"], "query": [], "variable": []}, "header": [], "method": "PUT", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"userId\": \"example\",\n    \"role\": \"OWNER\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Get Project Memberships", "request": {"description": "Get all memberships for a specific project (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/projects/:projectId/memberships", "host": ["{{baseUrl}}"], "path": ["api", "public", "projects", ":projectId", "memberships"], "query": [], "variable": [{"key": "projectId", "value": "", "description": null}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Update Project Membership", "request": {"description": "Create or update a membership for a specific project (requires organization-scoped API key). The user must already be a member of the organization.", "url": {"raw": "{{baseUrl}}/api/public/projects/:projectId/memberships", "host": ["{{baseUrl}}"], "path": ["api", "public", "projects", ":projectId", "memberships"], "query": [], "variable": [{"key": "projectId", "value": "", "description": null}]}, "header": [], "method": "PUT", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"userId\": \"example\",\n    \"role\": \"OWNER\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}]}, {"_type": "container", "description": null, "name": "Metrics", "item": [{"_type": "endpoint", "name": "Daily", "request": {"description": "Get daily metrics of the Langfuse project", "url": {"raw": "{{baseUrl}}/api/public/metrics/daily?page=&limit=&traceName=&userId=&tags=&environment=&fromTimestamp=&toTimestamp=", "host": ["{{baseUrl}}"], "path": ["api", "public", "metrics", "daily"], "query": [{"key": "page", "value": "", "description": "page number, starts at 1"}, {"key": "limit", "value": "", "description": "limit of items per page"}, {"key": "traceName", "value": "", "description": "Optional filter by the name of the trace"}, {"key": "userId", "value": "", "description": "Optional filter by the userId associated with the trace"}, {"key": "tags", "value": "", "description": "Optional filter for metrics where traces include all of these tags"}, {"key": "environment", "value": "", "description": "Optional filter for metrics where events include any of these environments"}, {"key": "fromTimestamp", "value": "", "description": "Optional filter to only include traces and observations on or after a certain datetime (ISO 8601)"}, {"key": "toTimestamp", "value": "", "description": "Optional filter to only include traces and observations before a certain datetime (ISO 8601)"}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Models", "item": [{"_type": "endpoint", "name": "Create", "request": {"description": "Create a model", "url": {"raw": "{{baseUrl}}/api/public/models", "host": ["{{baseUrl}}"], "path": ["api", "public", "models"], "query": [], "variable": []}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"modelName\": \"example\",\n    \"matchPattern\": \"example\",\n    \"startDate\": \"1994-11-05T13:15:30Z\",\n    \"unit\": \"CHARACTERS\",\n    \"inputPrice\": 0,\n    \"outputPrice\": 0,\n    \"totalPrice\": 0,\n    \"tokenizerId\": \"example\",\n    \"tokenizerConfig\": \"UNKNOWN\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "List", "request": {"description": "Get all models", "url": {"raw": "{{baseUrl}}/api/public/models?page=&limit=", "host": ["{{baseUrl}}"], "path": ["api", "public", "models"], "query": [{"key": "page", "value": "", "description": "page number, starts at 1"}, {"key": "limit", "value": "", "description": "limit of items per page"}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get", "request": {"description": "Get a model", "url": {"raw": "{{baseUrl}}/api/public/models/:id", "host": ["{{baseUrl}}"], "path": ["api", "public", "models", ":id"], "query": [], "variable": [{"key": "id", "value": "", "description": null}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Delete", "request": {"description": "Delete a model. Cannot delete models managed by Langfuse. You can create your own definition with the same modelName to override the definition though.", "url": {"raw": "{{baseUrl}}/api/public/models/:id", "host": ["{{baseUrl}}"], "path": ["api", "public", "models", ":id"], "query": [], "variable": [{"key": "id", "value": "", "description": null}]}, "header": [], "method": "DELETE", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Observations", "item": [{"_type": "endpoint", "name": "Get", "request": {"description": "Get a observation", "url": {"raw": "{{baseUrl}}/api/public/observations/:observationId", "host": ["{{baseUrl}}"], "path": ["api", "public", "observations", ":observationId"], "query": [], "variable": [{"key": "observationId", "value": "", "description": "The unique langfuse identifier of an observation, can be an event, span or generation"}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get Many", "request": {"description": "Get a list of observations", "url": {"raw": "{{baseUrl}}/api/public/observations?page=&limit=&name=&userId=&type=&traceId=&parentObservationId=&environment=&fromStartTime=&toStartTime=&version=", "host": ["{{baseUrl}}"], "path": ["api", "public", "observations"], "query": [{"key": "page", "value": "", "description": "Page number, starts at 1."}, {"key": "limit", "value": "", "description": "Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit."}, {"key": "name", "value": "", "description": null}, {"key": "userId", "value": "", "description": null}, {"key": "type", "value": "", "description": null}, {"key": "traceId", "value": "", "description": null}, {"key": "parentObservationId", "value": "", "description": null}, {"key": "environment", "value": "", "description": "Optional filter for observations where the environment is one of the provided values."}, {"key": "fromStartTime", "value": "", "description": "Retrieve only observations with a start_time or or after this datetime (ISO 8601)."}, {"key": "toStartTime", "value": "", "description": "Retrieve only observations with a start_time before this datetime (ISO 8601)."}, {"key": "version", "value": "", "description": "Optional filter to only include observations with a certain version."}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Projects", "item": [{"_type": "endpoint", "name": "Get", "request": {"description": "Get Project associated with API key", "url": {"raw": "{{baseUrl}}/api/public/projects", "host": ["{{baseUrl}}"], "path": ["api", "public", "projects"], "query": [], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Create", "request": {"description": "Create a new project (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/projects", "host": ["{{baseUrl}}"], "path": ["api", "public", "projects"], "query": [], "variable": []}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"name\": \"example\",\n    \"retention\": 0\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Update", "request": {"description": "Update a project by ID (requires organization-scoped API key).", "url": {"raw": "{{baseUrl}}/api/public/projects/:projectId", "host": ["{{baseUrl}}"], "path": ["api", "public", "projects", ":projectId"], "query": [], "variable": [{"key": "projectId", "value": "", "description": null}]}, "header": [], "method": "PUT", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"name\": \"example\",\n    \"retention\": 0\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Delete", "request": {"description": "Delete a project by ID (requires organization-scoped API key). Project deletion is processed asynchronously.", "url": {"raw": "{{baseUrl}}/api/public/projects/:projectId", "host": ["{{baseUrl}}"], "path": ["api", "public", "projects", ":projectId"], "query": [], "variable": [{"key": "projectId", "value": "", "description": null}]}, "header": [], "method": "DELETE", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get Api Keys", "request": {"description": "Get all API keys for a project (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/projects/:projectId/apiKeys", "host": ["{{baseUrl}}"], "path": ["api", "public", "projects", ":projectId", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "query": [], "variable": [{"key": "projectId", "value": "", "description": null}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Create Api Key", "request": {"description": "Create a new API key for a project (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/projects/:projectId/apiKeys", "host": ["{{baseUrl}}"], "path": ["api", "public", "projects", ":projectId", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "query": [], "variable": [{"key": "projectId", "value": "", "description": null}]}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"note\": \"example\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Delete Api Key", "request": {"description": "Delete an API key for a project (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/projects/:projectId/apiKeys/:apiKeyId", "host": ["{{baseUrl}}"], "path": ["api", "public", "projects", ":projectId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", ":apiKeyId"], "query": [], "variable": [{"key": "projectId", "value": "", "description": null}, {"key": "apiKeyId", "value": "", "description": null}]}, "header": [], "method": "DELETE", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Prompt Version", "item": [{"_type": "endpoint", "name": "Update", "request": {"description": "Update labels for a specific prompt version", "url": {"raw": "{{baseUrl}}/api/public/v2/prompts/:name/versions/:version", "host": ["{{baseUrl}}"], "path": ["api", "public", "v2", "prompts", ":name", "versions", ":version"], "query": [], "variable": [{"key": "name", "value": "", "description": "The name of the prompt"}, {"key": "version", "value": "", "description": "Version of the prompt to update"}]}, "header": [], "method": "PATCH", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"newLabels\": [\n        \"example\"\n    ]\n}", "options": {"raw": {"language": "json"}}}}, "response": []}]}, {"_type": "container", "description": null, "name": "Prompts", "item": [{"_type": "endpoint", "name": "Get", "request": {"description": "Get a prompt", "url": {"raw": "{{baseUrl}}/api/public/v2/prompts/:promptName?version=&label=", "host": ["{{baseUrl}}"], "path": ["api", "public", "v2", "prompts", ":prompt<PERSON>ame"], "query": [{"key": "version", "value": "", "description": "Version of the prompt to be retrieved."}, {"key": "label", "value": "", "description": "Label of the prompt to be retrieved. Defaults to \"production\" if no label or version is set."}], "variable": [{"key": "promptName", "value": "", "description": "The name of the prompt"}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "List", "request": {"description": "Get a list of prompt names with versions and labels", "url": {"raw": "{{baseUrl}}/api/public/v2/prompts?name=&label=&tag=&page=&limit=&fromUpdatedAt=&toUpdatedAt=", "host": ["{{baseUrl}}"], "path": ["api", "public", "v2", "prompts"], "query": [{"key": "name", "value": "", "description": null}, {"key": "label", "value": "", "description": null}, {"key": "tag", "value": "", "description": null}, {"key": "page", "value": "", "description": "page number, starts at 1"}, {"key": "limit", "value": "", "description": "limit of items per page"}, {"key": "fromUpdatedAt", "value": "", "description": "Optional filter to only include prompt versions created/updated on or after a certain datetime (ISO 8601)"}, {"key": "toUpdatedAt", "value": "", "description": "Optional filter to only include prompt versions created/updated before a certain datetime (ISO 8601)"}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Create", "request": {"description": "Create a new version for the prompt with the given `name`", "url": {"raw": "{{baseUrl}}/api/public/v2/prompts", "host": ["{{baseUrl}}"], "path": ["api", "public", "v2", "prompts"], "query": [], "variable": []}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"type\": \"chat\",\n    \"name\": \"example\",\n    \"prompt\": [\n        {\n            \"role\": \"example\",\n            \"content\": \"example\"\n        }\n    ],\n    \"config\": \"UNKNOWN\",\n    \"labels\": [\n        \"example\"\n    ],\n    \"tags\": [\n        \"example\"\n    ],\n    \"commitMessage\": \"example\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}]}, {"_type": "container", "description": null, "name": "<PERSON><PERSON>", "item": [{"_type": "endpoint", "name": "Get Service Provider Config", "request": {"description": "Get SCIM Service Provider Configuration (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/scim/ServiceProviderConfig", "host": ["{{baseUrl}}"], "path": ["api", "public", "scim", "ServiceProviderConfig"], "query": [], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get Resource Types", "request": {"description": "Get SCIM Resource Types (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/scim/ResourceTypes", "host": ["{{baseUrl}}"], "path": ["api", "public", "scim", "ResourceTypes"], "query": [], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "<PERSON>", "request": {"description": "Get SCIM Schemas (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/scim/Schemas", "host": ["{{baseUrl}}"], "path": ["api", "public", "scim", "<PERSON><PERSON><PERSON>"], "query": [], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "List Users", "request": {"description": "List users in the organization (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/scim/Users?filter=&startIndex=&count=", "host": ["{{baseUrl}}"], "path": ["api", "public", "scim", "Users"], "query": [{"key": "filter", "value": "", "description": "Filter expression (e.g. userName eq \"value\")"}, {"key": "startIndex", "value": "", "description": "1-based index of the first result to return (default 1)"}, {"key": "count", "value": "", "description": "Maximum number of results to return (default 100)"}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Create User", "request": {"description": "Create a new user in the organization (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/scim/Users", "host": ["{{baseUrl}}"], "path": ["api", "public", "scim", "Users"], "query": [], "variable": []}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"userName\": \"example\",\n    \"name\": {\n        \"formatted\": \"example\"\n    },\n    \"emails\": [\n        {\n            \"primary\": true,\n            \"value\": \"example\",\n            \"type\": \"example\"\n        }\n    ],\n    \"active\": true,\n    \"password\": \"example\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Get User", "request": {"description": "Get a specific user by ID (requires organization-scoped API key)", "url": {"raw": "{{baseUrl}}/api/public/scim/Users/<USER>", "host": ["{{baseUrl}}"], "path": ["api", "public", "scim", "Users", ":userId"], "query": [], "variable": [{"key": "userId", "value": "", "description": null}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Delete User", "request": {"description": "Remove a user from the organization (requires organization-scoped API key). Note that this only removes the user from the organization but does not delete the user entity itself.", "url": {"raw": "{{baseUrl}}/api/public/scim/Users/<USER>", "host": ["{{baseUrl}}"], "path": ["api", "public", "scim", "Users", ":userId"], "query": [], "variable": [{"key": "userId", "value": "", "description": null}]}, "header": [], "method": "DELETE", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Score Configs", "item": [{"_type": "endpoint", "name": "Create", "request": {"description": "Create a score configuration (config). Score configs are used to define the structure of scores", "url": {"raw": "{{baseUrl}}/api/public/score-configs", "host": ["{{baseUrl}}"], "path": ["api", "public", "score-configs"], "query": [], "variable": []}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"name\": \"example\",\n    \"dataType\": \"NUMERIC\",\n    \"categories\": [\n        {\n            \"value\": 0,\n            \"label\": \"example\"\n        }\n    ],\n    \"minValue\": 0,\n    \"maxValue\": 0,\n    \"description\": \"example\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Get", "request": {"description": "Get all score configs", "url": {"raw": "{{baseUrl}}/api/public/score-configs?page=&limit=", "host": ["{{baseUrl}}"], "path": ["api", "public", "score-configs"], "query": [{"key": "page", "value": "", "description": "Page number, starts at 1."}, {"key": "limit", "value": "", "description": "Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit"}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get By Id", "request": {"description": "Get a score config", "url": {"raw": "{{baseUrl}}/api/public/score-configs/:configId", "host": ["{{baseUrl}}"], "path": ["api", "public", "score-configs", ":configId"], "query": [], "variable": [{"key": "configId", "value": "", "description": "The unique langfuse identifier of a score config"}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Score", "item": [{"_type": "endpoint", "name": "Create", "request": {"description": "Create a score", "url": {"raw": "{{baseUrl}}/api/public/scores", "host": ["{{baseUrl}}"], "path": ["api", "public", "scores"], "query": [], "variable": []}, "header": [], "method": "POST", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"name\": \"novelty\",\n    \"value\": 0.9,\n    \"traceId\": \"cdef-1234-5678-90ab\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"_type": "endpoint", "name": "Get", "request": {"description": "Get a list of scores", "url": {"raw": "{{baseUrl}}/api/public/scores?page=&limit=&userId=&name=&fromTimestamp=&toTimestamp=&environment=&source=&operator=&value=&scoreIds=&configId=&queueId=&dataType=&traceTags=", "host": ["{{baseUrl}}"], "path": ["api", "public", "scores"], "query": [{"key": "page", "value": "", "description": "Page number, starts at 1."}, {"key": "limit", "value": "", "description": "Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit."}, {"key": "userId", "value": "", "description": "Retrieve only scores with this userId associated to the trace."}, {"key": "name", "value": "", "description": "Retrieve only scores with this name."}, {"key": "fromTimestamp", "value": "", "description": "Optional filter to only include scores created on or after a certain datetime (ISO 8601)"}, {"key": "toTimestamp", "value": "", "description": "Optional filter to only include scores created before a certain datetime (ISO 8601)"}, {"key": "environment", "value": "", "description": "Optional filter for scores where the environment is one of the provided values."}, {"key": "source", "value": "", "description": "Retrieve only scores from a specific source."}, {"key": "operator", "value": "", "description": "Retrieve only scores with <operator> value."}, {"key": "value", "value": "", "description": "Retrieve only scores with <operator> value."}, {"key": "scoreIds", "value": "", "description": "Comma-separated list of score IDs to limit the results to."}, {"key": "configId", "value": "", "description": "Retrieve only scores with a specific configId."}, {"key": "queueId", "value": "", "description": "Retrieve only scores with a specific annotation queueId."}, {"key": "dataType", "value": "", "description": "Retrieve only scores with a specific dataType."}, {"key": "traceTags", "value": "", "description": "Only scores linked to traces that include all of these tags will be returned."}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get By Id", "request": {"description": "Get a score", "url": {"raw": "{{baseUrl}}/api/public/scores/:scoreId", "host": ["{{baseUrl}}"], "path": ["api", "public", "scores", ":scoreId"], "query": [], "variable": [{"key": "scoreId", "value": "", "description": "The unique langfuse identifier of a score"}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Delete", "request": {"description": "Delete a score", "url": {"raw": "{{baseUrl}}/api/public/scores/:scoreId", "host": ["{{baseUrl}}"], "path": ["api", "public", "scores", ":scoreId"], "query": [], "variable": [{"key": "scoreId", "value": "", "description": "The unique langfuse identifier of a score"}]}, "header": [], "method": "DELETE", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Sessions", "item": [{"_type": "endpoint", "name": "List", "request": {"description": "Get sessions", "url": {"raw": "{{baseUrl}}/api/public/sessions?page=&limit=&fromTimestamp=&toTimestamp=&environment=", "host": ["{{baseUrl}}"], "path": ["api", "public", "sessions"], "query": [{"key": "page", "value": "", "description": "Page number, starts at 1"}, {"key": "limit", "value": "", "description": "Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit."}, {"key": "fromTimestamp", "value": "", "description": "Optional filter to only include sessions created on or after a certain datetime (ISO 8601)"}, {"key": "toTimestamp", "value": "", "description": "Optional filter to only include sessions created before a certain datetime (ISO 8601)"}, {"key": "environment", "value": "", "description": "Optional filter for sessions where the environment is one of the provided values."}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Get", "request": {"description": "Get a session. Please note that `traces` on this endpoint are not paginated, if you plan to fetch large sessions, consider `GET /api/public/traces?sessionId=<sessionId>`", "url": {"raw": "{{baseUrl}}/api/public/sessions/:sessionId", "host": ["{{baseUrl}}"], "path": ["api", "public", "sessions", ":sessionId"], "query": [], "variable": [{"key": "sessionId", "value": "", "description": "The unique id of a session"}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}]}, {"_type": "container", "description": null, "name": "Trace", "item": [{"_type": "endpoint", "name": "Get", "request": {"description": "Get a specific trace", "url": {"raw": "{{baseUrl}}/api/public/traces/:traceId", "host": ["{{baseUrl}}"], "path": ["api", "public", "traces", ":traceId"], "query": [], "variable": [{"key": "traceId", "value": "", "description": "The unique langfuse identifier of a trace"}]}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Delete", "request": {"description": "Delete a specific trace", "url": {"raw": "{{baseUrl}}/api/public/traces/:traceId", "host": ["{{baseUrl}}"], "path": ["api", "public", "traces", ":traceId"], "query": [], "variable": [{"key": "traceId", "value": "", "description": "The unique langfuse identifier of the trace to delete"}]}, "header": [], "method": "DELETE", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "List", "request": {"description": "Get list of traces", "url": {"raw": "{{baseUrl}}/api/public/traces?page=&limit=&userId=&name=&sessionId=&fromTimestamp=&toTimestamp=&orderBy=&tags=&version=&release=&environment=", "host": ["{{baseUrl}}"], "path": ["api", "public", "traces"], "query": [{"key": "page", "value": "", "description": "Page number, starts at 1"}, {"key": "limit", "value": "", "description": "Limit of items per page. If you encounter api issues due to too large page sizes, try to reduce the limit."}, {"key": "userId", "value": "", "description": null}, {"key": "name", "value": "", "description": null}, {"key": "sessionId", "value": "", "description": null}, {"key": "fromTimestamp", "value": "", "description": "Optional filter to only include traces with a trace.timestamp on or after a certain datetime (ISO 8601)"}, {"key": "toTimestamp", "value": "", "description": "Optional filter to only include traces with a trace.timestamp before a certain datetime (ISO 8601)"}, {"key": "orderBy", "value": "", "description": "Format of the string [field].[asc/desc]. Fields: id, timestamp, name, userId, release, version, public, bookmarked, sessionId. Example: timestamp.asc"}, {"key": "tags", "value": "", "description": "Only traces that include all of these tags will be returned."}, {"key": "version", "value": "", "description": "Optional filter to only include traces with a certain version."}, {"key": "release", "value": "", "description": "Optional filter to only include traces with a certain release."}, {"key": "environment", "value": "", "description": "Optional filter for traces where the environment is one of the provided values."}], "variable": []}, "header": [], "method": "GET", "auth": null, "body": null}, "response": []}, {"_type": "endpoint", "name": "Delete Multiple", "request": {"description": "Delete multiple traces", "url": {"raw": "{{baseUrl}}/api/public/traces", "host": ["{{baseUrl}}"], "path": ["api", "public", "traces"], "query": [], "variable": []}, "header": [], "method": "DELETE", "auth": null, "body": {"mode": "raw", "raw": "{\n    \"traceIds\": [\n        \"example\"\n    ]\n}", "options": {"raw": {"language": "json"}}}}, "response": []}]}]}
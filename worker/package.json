{"name": "worker", "version": "3.51.0", "description": "", "license": "MIT", "private": true, "engines": {"node": "20"}, "scripts": {"test": "dotenv -e ../.env -- vitest run --reporter=basic --pool=forks --poolOptions.forks.singleFork=true", "coverage": "vitest run --coverage", "start": "dotenv -e ../.env -- node dist/index.js", "build": "tsc", "dev": "dotenv -e ../.env -- nodemon src/index.ts", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --max-warnings 32", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "refill-ingestion-events": "dotenv -e ../.env -- tsx src/scripts/refill-ingestion-events.ts", "refill-billing-event": "dotenv -e ../.env -- tsx src/scripts/refill-billing-event.ts"}, "author": "<EMAIL>", "dependencies": {"@anthropic-ai/tokenizer": "^0.0.4", "@appsignal/opentelemetry-instrumentation-bullmq": "^0.7.3", "@clickhouse/client": "^1.4.0", "@langfuse/shared": "workspace:*", "@opentelemetry/api": "^1.9.0", "@opentelemetry/exporter-trace-otlp-proto": "^0.53.0", "@opentelemetry/instrumentation": "^0.53.0", "@opentelemetry/instrumentation-aws-sdk": "^0.44.0", "@opentelemetry/instrumentation-express": "^0.43.0", "@opentelemetry/instrumentation-http": "^0.53.0", "@opentelemetry/instrumentation-ioredis": "^0.43.0", "@opentelemetry/instrumentation-winston": "^0.40.0", "@opentelemetry/resource-detector-aws": "^1.6.2", "@opentelemetry/resource-detector-container": "^0.4.2", "@opentelemetry/resources": "^1.26.0", "@opentelemetry/sdk-node": "^0.53.0", "@prisma/instrumentation": "^6.3.0", "backoff": "^2.5.0", "bullmq": "^5.34.10", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dd-trace": "^5.36.0", "decimal.js": "^10.4.3", "dotenv": "^16.4.5", "exponential-backoff": "^3.1.1", "express": "^4.21.0", "handlebars": "^4.7.8", "helmet": "^7.1.0", "ioredis": "^5.4.1", "jsonpath-plus": "10.3.0", "kysely": "^0.27.4", "lodash": "^4.17.21", "pg": "^8.13.0", "posthog-node": "^4.3.1", "stripe": "^17.4.0", "tiktoken": "^1.0.20", "uuid": "^9.0.1", "zod": "^3.23.8"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-serve-static-core": "^4.19.3", "@types/lodash": "^4.17.10", "@types/node": "^20.11.19", "@types/pg": "^8.11.10", "@types/uuid": "^9.0.8", "@typescript-eslint/parser": "^7.12.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-prettier": "^5.1.3", "kysely-codegen": "^0.16.8", "msw": "^2.6.5", "nodemon": "^3.1.7", "prettier": "^3.3.3", "ts-node": "^10.9.2", "tsc-watch": "^6.2.0", "tsx": "^4.19.1", "typescript": "^5.4.5", "vitest": "^2.1.2", "wait-for-expect": "^3.0.2"}}